<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Card from '$lib/components/ui/card/index.js';
	import { goto } from '$app/navigation';

	import Markdown from '$lib/components/Markdown.svelte';
	import { Tabs, TabsContent, TabsList, TabsTrigger } from '$lib/components/ui/tabs';

	let eventData = {
		title: '',
		shortDescription: '',
		description: '',
		descriptionArabic: '',
		startDate: '',
		startTime: '',
		endDate: '',
		endTime: '',
		location: '',
		locationUrl: '',
		maxRegistrations: ''
	};

	let bannerFile: FileList;
	let previewUrl: string | null = null;
	let errorMessage: string | null = null;

	function handleFileSelect(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files[0]) {
			const file = input.files[0];
			const img = new Image();
			img.src = URL.createObjectURL(file);

			img.onload = () => {
				if (file.size > 2 * 1024 * 1024) {
					errorMessage = 'File size exceeds 2MB';
					previewUrl = null;
				} else if (img.width > 1920 || img.height > 1093) {
					errorMessage = 'Image dimensions exceed 1920x1093 pixels';
					previewUrl = null;
				} else {
					errorMessage = null;
					previewUrl = img.src;
				}
			};
		}
	}

	function handleCancel() {
		goto('/private/events');
	}
</script>

<div class="container mx-auto px-4">
	<Card.Root class="mx-auto max-w-2xl">
		<Card.Header>
			<Card.Title class="text-2xl font-bold">Create New Event</Card.Title>
			<Card.Description>Fill in the details to create your event</Card.Description>
		</Card.Header>
		<form class="" method="POST" enctype="multipart/form-data">
			<Card.Content class="space-y-6">
				<!-- Banner Image Upload -->
				<div class="space-y-2">
					<Label for="banner">Event Banner</Label>
					<div class="flex flex-col gap-4">
						{#if previewUrl}
							<img
								src={previewUrl}
								alt="Banner preview"
								class="h-auto w-full rounded-lg object-cover"
							/>
						{:else}
							<div class="flex h-48 w-full items-center justify-center rounded-lg bg-gray-100">
								<span class="text-gray-500">Banner preview</span>
							</div>
						{/if}
						<Input
							id="banner"
							name="banner"
							type="file"
							accept="image/*"
							bind:value={bannerFile}
							onchange={handleFileSelect}
							class="cursor-pointer"
						/>
						<p class="text-sm text-gray-500">
							Recommended size: 1920x1093 pixels. Max file size: 2 MB
						</p>
						{#if errorMessage}
							<p class="text-sm text-red-500">{errorMessage}</p>
						{/if}
					</div>
				</div>

				<!-- Event Title -->
				<div class="mt-3 space-y-2">
					<Label for="title">Event Title</Label>
					<Input id="title" name="title" placeholder="Enter event title" required />
				</div>

				<!-- Short Description -->
				<div class="space-y-2">
					<Label for="shortDescription">Short Description</Label>
					<Input
						id="shortDescription"
						bind:value={eventData.shortDescription}
						name="shortDescription"
						placeholder="Brief description for event listings (max 150 characters)"
						required
					/>
					<p class="text-sm text-gray-500">
						{eventData.shortDescription.length}/150 characters
					</p>
				</div>

				<!-- Full Description -->
				<!-- <div class="mt-3 space-y-2">
					<Label for="longDescription">Full Description</Label>
					<Textarea
						id="longDescription"
						name="longDescription"
						placeholder="Provide detailed information about your event"
						required
					/>
				</div> -->

				<!-- English Description MARKDOWN -->
				<div class="mb-4">
					<label for="longDescription" class="mb-1 block text-sm font-medium"
						>Event Description</label
					>
					<Tabs value="write" class="w-full">
						<TabsList>
							<TabsTrigger value="write">Write</TabsTrigger>
							<TabsTrigger value="preview">Preview</TabsTrigger>
						</TabsList>
						<TabsContent value="write">
							<Textarea
								id="longDescription"
								name="longDescription"
								bind:value={eventData.description}
								placeholder="Write your event description using Markdown..."
								rows="10"
								class="w-full"
							/>
						</TabsContent>
						<TabsContent value="preview">
							<div class="min-h-[200px] rounded-md border p-4">
								<Markdown content={eventData.description} />
							</div>
						</TabsContent>
					</Tabs>
				</div>
				<!-- Full Description Arabic -->
				<!-- <div class="mt-3 space-y-2">
					<Label for="longDescription">Full Description - Arabic</Label>
					<Textarea
						id="longDescriptionArabic"
						name="longDescriptionArabic"
						placeholder="Provide detailed information about your event in Arabic"
					/>
				</div> -->

				<!-- Arabic Description MARKDOWN -->
				<div class="mb-4">
					<label for="longDescriptionArabic" class="mb-1 block text-sm font-medium"
						>Event Description - Arabic</label
					>
					<Tabs value="write" class="w-full">
						<TabsList>
							<TabsTrigger value="write">Write</TabsTrigger>
							<TabsTrigger value="preview">Preview</TabsTrigger>
						</TabsList>
						<TabsContent value="write">
							<Textarea
								id="longDescriptionArabic"
								name="longDescriptionArabic"
								bind:value={eventData.descriptionArabic}
								placeholder="اكتب وصف الحدث باستخدام Markdown..."
								rows="10"
								class="w-full text-right"
								dir="rtl"
							/>
						</TabsContent>
						<TabsContent value="preview">
							<div class="min-h-[200px] rounded-md border p-4">
								<Markdown content={eventData.descriptionArabic} isRTL={true} />
							</div>
						</TabsContent>
					</Tabs>
					<p class="mt-1 text-right text-xs text-muted-foreground" dir="rtl">
						يدعم Markdown: **غامق**، *مائل*، [روابط](https://example.com)، إلخ.
					</p>
				</div>

				<!-- Date and Time Section -->
				<div class="space-y-4">
					<h3 class="font-medium">Date and Time</h3>

					<!-- Start Date and Time -->
					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="space-y-2">
							<Label for="startDate">Start Date</Label>
							<Input
								id="startDate"
								type="date"
								name="startDate"
								bind:value={eventData.startDate}
								required
							/>
						</div>
						<div class="space-y-2">
							<Label for="startTime">Start Time</Label>
							<Input
								id="startTime"
								name="startTime"
								type="time"
								bind:value={eventData.startTime}
								required
							/>
						</div>
					</div>

					<!-- End Date and Time -->
					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div class="space-y-2">
							<Label for="endDate">End Date</Label>
							<Input
								id="endDate"
								type="date"
								name="endDate"
								bind:value={eventData.endDate}
								required
							/>
						</div>
						<div class="space-y-2">
							<Label for="endTime">End Time</Label>
							<Input
								id="endTime"
								name="endTime"
								type="time"
								bind:value={eventData.endTime}
								required
							/>
						</div>
					</div>
				</div>

				<!-- Location -->
				<div class="space-y-2">
					<Label for="location">Location</Label>
					<Input
						id="location"
						name="location"
						bind:value={eventData.location}
						placeholder="Enter event location or meeting link"
						required
					/>
				</div>
				<!-- Location URL -->
				<div class="space-y-2">
					<Label for="location">Location URL</Label>
					<Input
						id="locationUrl"
						name="locationUrl"
						bind:value={eventData.locationUrl}
						placeholder="Enter location url"
						required
					/>
				</div>

				<!-- Max Registrations -->
				<!-- <div class="space-y-2">
                    <Label for="maxParticipants">Maximum Registrations</Label>
                    <Input
                        id="maxRegistrations"
                        type="number"
                        name="maxRegistrations"
                        bind:value={eventData.maxRegistrations}
                        placeholder="Enter maximum number of Registrations"
                        min="1"
                    />
                </div> -->
			</Card.Content>

			<Card.Footer class="flex justify-end space-x-4">
				<Button variant="outline" onclick={handleCancel} type="button">Cancel</Button>
				<Button type="submit">Create Event</Button>
			</Card.Footer>
		</form>
	</Card.Root>
</div>
