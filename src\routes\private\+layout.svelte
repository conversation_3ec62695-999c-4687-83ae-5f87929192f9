<script>
	export let data;
	$: ({ supabase } = data);

	$: logout = async () => {
		const { error } = await supabase.auth.signOut();
		if (error) {
			console.error(error);
		}
		window.location.href = '/';
	};
</script>

<div class="flex w-screen h-screen">
	<!-- Sidebar Navigation -->
	<nav class="ml-3 w-36 bg-white shadow-md">
		<!-- <div class="p-4">
			<h1 class="text-2xl font-semibold text-gray-800">Admin Panel</h1>
		</div> -->
		<ul class=" mt-4">
			<li>
				<a href="/private/events" class="block px-4 py-2 text-gray-600 hover:bg-gray-200">Events</a>
			</li>
			<li>
				<a href="/private/users" class="block px-4 py-2 text-gray-600 hover:bg-gray-200">Users</a>
			</li>

			<li>
				<button on:click={logout} class="block px-4 py-2 text-gray-600 hover:bg-gray-200"
					>Logout</button
				>
			</li>
		</ul>
	</nav>

	<!-- Main Content Area -->
	<main class="flex-1 p-8">
		<slot />
	</main>
</div>
