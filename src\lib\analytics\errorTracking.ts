import { analytics } from './analytics';
import { browser } from '$app/environment';

// Enhanced error tracking utilities
export class ErrorTracker {
	private static instance: ErrorTracker;

	private constructor() {}

	public static getInstance(): ErrorTracker {
		if (!ErrorTracker.instance) {
			ErrorTracker.instance = new ErrorTracker();
		}
		return ErrorTracker.instance;
	}

	// Track API errors with detailed context
	trackAPIError(
		endpoint: string,
		method: string,
		statusCode: number,
		errorMessage: string,
		requestData?: any
	): void {
		analytics.trackAPIError(endpoint, statusCode, errorMessage);
		
		// Also track as general error with more context
		analytics.trackError('api_error', errorMessage, {
			endpoint,
			method,
			status_code: statusCode,
			request_data: requestData ? JSON.stringify(requestData) : undefined,
			timestamp: new Date().toISOString()
		});
	}

	// Track form submission errors
	trackFormError(
		formType: string,
		errorMessage: string,
		formData?: Record<string, any>
	): void {
		analytics.trackError('form_error', errorMessage, {
			form_type: formType,
			form_data: formData ? JSON.stringify(formData) : undefined,
			timestamp: new Date().toISOString()
		});
	}

	// Track navigation errors
	trackNavigationError(
		fromPath: string,
		toPath: string,
		errorMessage: string
	): void {
		analytics.trackError('navigation_error', errorMessage, {
			from_path: fromPath,
			to_path: toPath,
			timestamp: new Date().toISOString()
		});
	}

	// Track component errors
	trackComponentError(
		componentName: string,
		errorMessage: string,
		props?: Record<string, any>
	): void {
		analytics.trackError('component_error', errorMessage, {
			component_name: componentName,
			props: props ? JSON.stringify(props) : undefined,
			timestamp: new Date().toISOString()
		});
	}

	// Track authentication errors
	trackAuthError(
		authAction: string,
		errorMessage: string,
		userEmail?: string
	): void {
		analytics.trackError('auth_error', errorMessage, {
			auth_action: authAction,
			user_email: userEmail,
			timestamp: new Date().toISOString()
		});
	}

	// Track database/Supabase errors
	trackDatabaseError(
		operation: string,
		table: string,
		errorMessage: string,
		query?: string
	): void {
		analytics.trackError('database_error', errorMessage, {
			operation,
			table,
			query,
			timestamp: new Date().toISOString()
		});
	}

	// Track performance issues
	trackPerformanceIssue(
		metric: string,
		value: number,
		threshold: number,
		context?: Record<string, any>
	): void {
		analytics.trackPerformance(metric, value, {
			threshold,
			is_slow: value > threshold,
			...context
		});

		// Also track as error if performance is significantly degraded
		if (value > threshold * 2) {
			analytics.trackError('performance_issue', `${metric} is ${value}ms (threshold: ${threshold}ms)`, {
				metric,
				value,
				threshold,
				...context
			});
		}
	}
}

// Export singleton instance
export const errorTracker = ErrorTracker.getInstance();

// Enhanced fetch wrapper with automatic error tracking
export async function trackedFetch(
	url: string,
	options: RequestInit = {},
	context?: Record<string, any>
): Promise<Response> {
	const startTime = performance.now();
	
	try {
		const response = await fetch(url, options);
		const endTime = performance.now();
		const duration = endTime - startTime;

		// Track API performance
		analytics.trackPerformance('api_request_duration', duration, {
			endpoint: url,
			method: options.method || 'GET',
			status_code: response.status,
			...context
		});

		// Track slow API calls
		if (duration > 5000) { // 5 seconds threshold
			errorTracker.trackPerformanceIssue('api_request_duration', duration, 5000, {
				endpoint: url,
				method: options.method || 'GET',
				status_code: response.status,
				...context
			});
		}

		// Track API errors for non-2xx responses
		if (!response.ok) {
			const errorText = await response.text().catch(() => 'Unknown error');
			errorTracker.trackAPIError(
				url,
				options.method || 'GET',
				response.status,
				errorText,
				options.body
			);
		}

		return response;
	} catch (error) {
		const endTime = performance.now();
		const duration = endTime - startTime;

		// Track network errors
		const errorMessage = error instanceof Error ? error.message : 'Network error';
		errorTracker.trackAPIError(
			url,
			options.method || 'GET',
			0, // Network error
			errorMessage,
			options.body
		);

		// Track performance for failed requests
		analytics.trackPerformance('api_request_duration', duration, {
			endpoint: url,
			method: options.method || 'GET',
			status_code: 0,
			error: errorMessage,
			...context
		});

		throw error;
	}
}

// Performance monitoring utilities
export function trackPageLoadTime(): void {
	if (!browser) return;

	// Track page load performance
	window.addEventListener('load', () => {
		setTimeout(() => {
			const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
			
			if (navigation) {
				const loadTime = navigation.loadEventEnd - navigation.fetchStart;
				const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;
				const firstPaint = performance.getEntriesByName('first-paint')[0]?.startTime || 0;
				const firstContentfulPaint = performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0;

				// Track various performance metrics
				analytics.trackPerformance('page_load_time', loadTime, {
					url: window.location.pathname,
					dom_content_loaded: domContentLoaded,
					first_paint: firstPaint,
					first_contentful_paint: firstContentfulPaint
				});

				// Track slow page loads
				if (loadTime > 3000) { // 3 seconds threshold
					errorTracker.trackPerformanceIssue('page_load_time', loadTime, 3000, {
						url: window.location.pathname
					});
				}
			}
		}, 0);
	});
}

// Initialize performance tracking
export function initializePerformanceTracking(): void {
	if (!browser) return;

	trackPageLoadTime();

	// Track long tasks (blocking the main thread)
	if ('PerformanceObserver' in window) {
		try {
			const observer = new PerformanceObserver((list) => {
				for (const entry of list.getEntries()) {
					if (entry.duration > 50) { // Tasks longer than 50ms
						analytics.trackPerformance('long_task_duration', entry.duration, {
							entry_type: entry.entryType,
							start_time: entry.startTime
						});
					}
				}
			});
			observer.observe({ entryTypes: ['longtask'] });
		} catch (error) {
			console.warn('Long task observer not supported:', error);
		}
	}
}
