<script lang="ts">
	import { page } from '$app/stores';
	import FormFieldManager from '$lib/components/admin/FormFieldManager.svelte';
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';

	import { AlertCircle, CheckCircle } from 'lucide-svelte';
	import { enhance } from '$app/forms';

	export let data: PageData;

	$: eventId = $page.params.eventid;
	$: eventTitle = data.event?.title || 'Event';
	$: isNewEvent = $page.url.searchParams.get('new') === 'true';
	$: isPublished = data.event?.is_published || false;
</script>

<svelte:head>
	<title>Form Builder - {eventTitle}</title>
</svelte:head>

{#if isNewEvent}
	<div class="mb-6 rounded-lg border border-blue-200 bg-blue-50 p-4">
		<div class="flex items-start space-x-3">
			<AlertCircle class="mt-0.5 h-5 w-5 text-blue-600" />
			<div>
				<h3 class="font-medium text-blue-900">Welcome! Configure Your Registration Form</h3>
				<p class="mt-1 text-sm text-blue-700">
					Your event has been created as a draft. Configure the registration form fields below, then
					publish your event to make it visible to the public.
				</p>
			</div>
		</div>
	</div>
{/if}

{#if !isPublished}
	<div class="mb-6 flex items-center justify-between rounded-lg border bg-blue-50 p-4">
		<div class="flex items-center space-x-3">
			<AlertCircle class="h-5 w-5 text-blue-600" />
			<div>
				<h3 class="font-medium text-blue-900">Event is in Draft Mode</h3>
				<p class="text-sm text-blue-700">Configure your form fields and publish when ready.</p>
			</div>
		</div>
		<form method="POST" action="?/publishEvent" use:enhance>
			<Button type="submit" class="bg-green-600 hover:bg-green-700">
				<CheckCircle class="mr-2 h-4 w-4" />
				Publish Event
			</Button>
		</form>
	</div>
{/if}

<FormFieldManager {eventId} {eventTitle} />
