<script lang="ts">
	import { goto } from '$app/navigation';
	import { But<PERSON> } from '$lib/components/ui/button';
	import * as Table from '$lib/components/ui/table/index.js';
	import type { PageData } from './$types';

	import DataTable from './data-table.svelte';
	import { columns, type Guest } from './columns.js';

	export let data;
	// To change the "dataa" variable, you need to change the "data" variable in the parent component. ie in data-table.svelte
	let dataa = data.guestsData;
</script>

<header class="m-4 flex flex-row items-center justify-between">
	<h1 class=" text-4xl font-bold">All Guests ({data.count})</h1>

	<div class=" space-y-3">
		<Button href="#">Add Guest</Button>
		<Button href="/private/events/{data.params.eventid}/guestlist/import">Import Guests</Button>
		<Button href="/private/events/{data.params.eventid}/guestlist/qr-scanner">Scan QR Code</Button>
		<Button
			href="/private/events/{data.params.eventid}/guestlist/api/export-csv"
			download="exported_data.csv">Export</Button
		>
		<Button href="/private/events/{data.params.eventid}/guestlist/cert">Certificates</Button>
		<Button href="/private/events/{data.params.eventid}/guestlist/waiting-list"
			>View Waiting List</Button
		>
	</div>
</header>

<main>
	<div class="container mx-auto py-10">
		<DataTable {dataa} {columns} />
	</div>
</main>
