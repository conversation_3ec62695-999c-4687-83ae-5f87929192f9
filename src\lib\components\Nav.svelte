<script lang="ts">
	import * as Select from '$lib/components/ui/select/index.js';
	import logo from '$lib/images/logo.png';
	import { langCodeShort } from '$lib/utils/langCode.js';
</script>

<div class="container flex h-16 items-center justify-between space-x-4">
	<div class="flex items-center gap-6 md:gap-8">
		<a href="/"><img alt="Logo" class="h-10 w-auto" src={logo} /></a>
		<nav class="hidden gap-6 md:flex">
			<a class="text-base font-medium text-primary" href="/">Events </a>
			<a
				class="text-base font-medium text-muted-foreground transition-colors hover:text-primary"
				href="/"
				>Calendar
			</a>
			<a
				class="text-base font-medium text-muted-foreground transition-colors hover:text-primary"
				href="/"
				>About
			</a>
		</nav>
	</div>
	<div class="flex items-center gap-4">
		<Select.Root type="single">
			<Select.Trigger class="w-[100px]">English</Select.Trigger>
			<Select.Content>
				{#each Object.keys(langCodeShort) as key}
					<Select.Item value={key}>{langCodeShort[key]}</Select.Item>
				{/each}
				<!-- <Select.Item value="en">English</Select.Item>
				<Select.Item value="ar">العربية</Select.Item> -->
			</Select.Content>
		</Select.Root>
	</div>
</div>
