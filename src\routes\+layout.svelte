<script>
	import '../app.css';
	import { invalidate } from '$app/navigation';
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import Nav from '$lib/components/Nav.svelte';
	import Footer from '$lib/components/Footer.svelte';
	import { initPostHog } from '$lib/analytics/posthog';
	import { analytics } from '$lib/analytics/analytics';
	import { initializePerformanceTracking } from '$lib/analytics/errorTracking';

	export let data;
	$: ({ session, supabase } = data);

	// Track page views when route changes
	$: if ($page.url.pathname) {
		analytics.trackPageView($page.url.pathname, {
			page_title: document?.title,
			referrer: document?.referrer
		});
	}

	onMount(() => {
		console.log('v0.0.27 -- 202504131740');

		// Initialize PostHog analytics
		initPostHog();

		// Initialize performance tracking
		initializePerformanceTracking();

		const { data } = supabase.auth.onAuthStateChange((_, newSession) => {
			if (newSession?.expires_at !== session?.expires_at) {
				invalidate('supabase:auth');
			}
		});

		return () => data.subscription.unsubscribe();
	});
</script>

<header
	class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
>
	<Nav />
</header>
<slot />
<!-- <Footer /> -->
